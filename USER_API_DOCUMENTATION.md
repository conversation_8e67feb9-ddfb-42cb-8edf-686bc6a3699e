# API Cập nhật User với Organization Member

## Tổng quan

API User đã đư<PERSON>c cập nhật để hỗ trợ cập nhật thông tin Organization Member cùng với thông tin User. <PERSON><PERSON> cập nhật User, bạn có thể truyền thêm tham số `organization` và `role` để cập nhật bảng Organization Member.

## Endpoint

```
PATCH /api/v2/backend/user/{user_id}/
```

## Authentication

Yêu cầu JWT token với quyền Admin (IsAdminUser).

```
Authorization: Bearer <access_token>
```

## Request Body

### Các trường cơ bản của User (không thay đổi):
- `fullname`: Họ tên người dùng
- `avatar`: Ảnh đại diện
- `phone`: <PERSON><PERSON> điện thoại
- `position`: ID của position
- `gender`: <PERSON><PERSON><PERSON><PERSON> t<PERSON>h
- `dob`: <PERSON><PERSON><PERSON>
- `address`: Địa chỉ

### Các trường mới cho Organization Member:
- `organization` (UUID, optional): ID của organization
- `role` (string, optional): Role trong organization (`admin` hoặc `member`)

## Ví dụ Request

### 1. Cập nhật User với Organization Member

```json
{
  "fullname": "Nguyễn Văn A",
  "phone": "0123456789",
  "organization": "550e8400-e29b-41d4-a716-************",
  "role": "admin"
}
```

### 2. Chỉ cập nhật thông tin User (không thay đổi Organization Member)

```json
{
  "fullname": "Nguyễn Văn B",
  "phone": "0987654321"
}
```

### 3. Chỉ cập nhật Organization Member

```json
{
  "organization": "550e8400-e29b-41d4-a716-************",
  "role": "member"
}
```

## Response

### Success Response (200 OK)

```json
{
  "id": 42,
  "email": "<EMAIL>",
  "avatar": null,
  "fullname": "Nguyễn Văn A",
  "phone": "0123456789",
  "position": {
    "id": 1,
    "title": "Developer"
  },
  "gender": null,
  "dob": null,
  "address": null,
  "system_role": "USER",
  "organization_memberships": [
    {
      "organization_id": "550e8400-e29b-41d4-a716-************",
      "organization_name": "Test Organization",
      "role": "admin",
      "is_active": true,
      "joined_at": "2025-08-07T02:53:08.973064Z"
    }
  ]
}
```

### Error Responses

#### 400 Bad Request - Invalid Role

```json
{
  "role": ["Role must be either 'admin' or 'member'"]
}
```

#### 400 Bad Request - Organization Not Found

```json
{
  "organization": ["Organization with id 550e8400-e29b-41d4-a716-************ does not exist"]
}
```

#### 401 Unauthorized

```json
{
  "detail": "Authentication credentials were not provided."
}
```

#### 403 Forbidden

```json
{
  "detail": "You do not have permission to perform this action."
}
```

## Hành vi của API

### 1. Cập nhật Organization Member

- Nếu User chưa có OrganizationMember với Organization được chỉ định:
  - Tạo mới OrganizationMember với role được chỉ định
  - Đặt `is_active = True`

- Nếu User đã có OrganizationMember với Organization được chỉ định:
  - Cập nhật role của OrganizationMember hiện tại

### 2. Validation

- `role` phải là `admin` hoặc `member`
- `organization` phải là UUID hợp lệ và tồn tại trong database
- Cả hai tham số `organization` và `role` phải được cung cấp cùng nhau

### 3. Response Data

- Response bao gồm tất cả thông tin User
- Thêm field `system_role` để phân biệt với organization role
- Thêm field `organization_memberships` chứa danh sách tất cả organization mà user tham gia

## Lưu ý

1. **Không thể cập nhật email và password** qua API này
2. **Cần cung cấp cả organization và role** nếu muốn cập nhật Organization Member
3. **API tương thích ngược** - các request không có organization/role vẫn hoạt động bình thường
4. **Unique constraint** - Một user chỉ có thể có một membership trong mỗi organization

## Test Script

Có thể sử dụng script `test_user_api.py` để test các chức năng của API:

```bash
python test_user_api.py
```

Script sẽ test:
- Tạo OrganizationMember mới
- Cập nhật role của OrganizationMember hiện tại
- Validation với role không hợp lệ
- Response format với organization_memberships
