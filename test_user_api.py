#!/usr/bin/env python
"""
Script để test API cập nhật User với Organization Member
"""
import os
import sys
import django
import json
from django.test import Client
from django.contrib.auth import get_user_model

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cls_backend.settings')
django.setup()

from organization.models import Organization, OrganizationMember
from organization.constants import ADMIN, MEMBER

User = get_user_model()

def create_test_data():
    """Tạo dữ liệu test"""
    print("Creating test data...")
    
    # Tạo superuser
    admin_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'pbkdf2_sha256$600000$test$hash',  # Dummy hash
            'fullname': 'Admin User',
            'is_superuser': True,
            'is_staff': True,
            'is_active': True
        }
    )
    if created:
        admin_user.set_password('testpass123')
        admin_user.save()
    
    # Tạo user thường
    test_user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'password': 'pbkdf2_sha256$600000$test$hash',  # Dummy hash
            'fullname': 'Test User',
            'is_active': True
        }
    )
    if created:
        test_user.set_password('testpass123')
        test_user.save()
    
    # Tạo organization
    organization, created = Organization.objects.get_or_create(
        name='Test Organization',
        defaults={
            'description': 'Test Description',
            'status': 'active'
        }
    )
    
    return admin_user, test_user, organization

def test_user_update_with_organization():
    """Test cập nhật user với thông tin organization"""
    print("\n=== Testing User Update with Organization ===")
    
    admin_user, test_user, organization = create_test_data()
    
    # Tạo client và login
    client = Client()
    
    # Login với admin user
    login_response = client.post('/auth/login', {
        'email': '<EMAIL>',
        'password': 'testpass123'
    }, content_type='application/json')
    
    if login_response.status_code != 200:
        print(f"Login failed: {login_response.status_code}")
        print(login_response.content.decode())
        return
    
    # Lấy token từ response
    login_data = json.loads(login_response.content.decode())
    token = login_data.get('access')
    
    if not token:
        print("No access token received")
        return
    
    print(f"Login successful, token: {token[:20]}...")
    
    # Test 1: Cập nhật user với organization và role
    print("\nTest 1: Update user with organization and role")
    
    update_data = {
        'fullname': 'Updated Test User',
        'organization': str(organization.id),
        'role': ADMIN
    }
    
    response = client.patch(
        f'/api/v2/backend/user/{test_user.id}/',
        json.dumps(update_data),
        content_type='application/json',
        HTTP_AUTHORIZATION=f'Bearer {token}'
    )
    
    print(f"Response status: {response.status_code}")
    print(f"Response content: {response.content.decode()}")
    
    if response.status_code == 200:
        # Kiểm tra OrganizationMember đã được tạo/cập nhật
        try:
            org_member = OrganizationMember.objects.get(
                user=test_user,
                organization=organization
            )
            print(f"✓ OrganizationMember created/updated: role={org_member.role}, active={org_member.is_active}")
        except OrganizationMember.DoesNotExist:
            print("✗ OrganizationMember not found")
    
    # Test 2: Cập nhật role của OrganizationMember đã tồn tại
    print("\nTest 2: Update existing OrganizationMember role")
    
    update_data = {
        'organization': str(organization.id),
        'role': MEMBER
    }
    
    response = client.patch(
        f'/api/v2/backend/user/{test_user.id}/',
        json.dumps(update_data),
        content_type='application/json',
        HTTP_AUTHORIZATION=f'Bearer {token}'
    )
    
    print(f"Response status: {response.status_code}")
    print(f"Response content: {response.content.decode()}")
    
    if response.status_code == 200:
        # Kiểm tra role đã được cập nhật
        org_member = OrganizationMember.objects.get(
            user=test_user,
            organization=organization
        )
        print(f"✓ Role updated to: {org_member.role}")
    
    # Test 3: Test với role không hợp lệ
    print("\nTest 3: Test with invalid role")
    
    update_data = {
        'organization': str(organization.id),
        'role': 'invalid_role'
    }
    
    response = client.patch(
        f'/api/v2/backend/user/{test_user.id}/',
        json.dumps(update_data),
        content_type='application/json',
        HTTP_AUTHORIZATION=f'Bearer {token}'
    )
    
    print(f"Response status: {response.status_code}")
    print(f"Response content: {response.content.decode()}")
    
    # Test 4: Test với chỉ organization mà không có role
    print("\nTest 4: Test with only organization (no role)")

    update_data = {
        'organization': str(organization.id)
        # Không có role
    }

    response = client.patch(
        f'/api/v2/backend/user/{test_user.id}/',
        json.dumps(update_data),
        content_type='application/json',
        HTTP_AUTHORIZATION=f'Bearer {token}'
    )

    print(f"Response status: {response.status_code}")
    print(f"Response content: {response.content.decode()}")

    # Test 5: Test với chỉ role mà không có organization
    print("\nTest 5: Test with only role (no organization)")

    update_data = {
        'role': ADMIN
        # Không có organization
    }

    response = client.patch(
        f'/api/v2/backend/user/{test_user.id}/',
        json.dumps(update_data),
        content_type='application/json',
        HTTP_AUTHORIZATION=f'Bearer {token}'
    )

    print(f"Response status: {response.status_code}")
    print(f"Response content: {response.content.decode()}")

    # Test 6: Lấy thông tin user để xem organization_memberships
    print("\nTest 6: Get user info with organization_memberships")

    response = client.get(
        f'/api/v2/backend/user/{test_user.id}/',
        HTTP_AUTHORIZATION=f'Bearer {token}'
    )

    print(f"Response status: {response.status_code}")
    if response.status_code == 200:
        user_data = json.loads(response.content.decode())
        print(f"User fullname: {user_data.get('fullname')}")
        print(f"Organization memberships: {user_data.get('organization_memberships', [])}")

if __name__ == '__main__':
    test_user_update_with_organization()
